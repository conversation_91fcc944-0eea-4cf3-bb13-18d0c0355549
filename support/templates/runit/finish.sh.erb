#!/bin/sh
set -e

exec 2>&1
cd <%= gdk_root %>

EXIT_CODE=$1
EXIT_CODE_FILE="<%= sv_dir %>/last_exit_code"

if [ -f "$EXIT_CODE_FILE" ]; then
  PREV_EXIT_CODE=$(cat "$EXIT_CODE_FILE" 2>/dev/null || echo "")
else
  PREV_EXIT_CODE=""
fi

if [ "$EXIT_CODE" = "0" -o "$EXIT_CODE" != "$PREV_EXIT_CODE" ]; then
  gdk send-telemetry service_finish '<%= service_instance.name %>' \
    --extra='command:<%= redacted_command.gsub(/'/, "''") %>' \
    --extra="exit_code:$EXIT_CODE" &
fi

echo "$EXIT_CODE" > "$EXIT_CODE_FILE"
