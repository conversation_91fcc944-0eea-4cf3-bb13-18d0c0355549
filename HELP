# GitLab Development Kit

Usage: gdk <command> [<args>]

Manage services:

  gdk start                                         # Start everything
  gdk start redis postgresql                        # Start specific services
  gdk stop                                          # Stop all services and unload Runit
  gdk stop redis postgresql                         # Stop specific service
  gdk status                                        # See status of all services
  gdk restart                                       # Restart everything
  gdk restart redis postgresql                      # Restart specific services
  gdk kill                                          # Forcibly kills services

  gdk tail                                          # Tail logs for all services (stdout and stderr only)
  gdk tail redis postgresql                         # Tail specific logs

  gdk psql [-d gitlabhq_development]                # Run Postgres console
  gdk psql-geo                                      # Run Postgres console connected to the Geo tracking database
  gdk rails <command> [<args>]                      # Execute provided command on Rails bundled with GitLab
  gdk redis-cli                                     # Run Redis console
  gdk clickhouse                                    # Run ClickHouse console

  gdk measure                                       # Generate a sitespeed.io report for given URL(s)
  gdk measure-workflow                              # Generate a sitespeed.io report for given workflow(s)

Manage GDK:

  gdk open                                          # Visit your GitLab installation in your default web browser

  gdk install gitlab_repo=https://my-fork           # Install everything
  gdk update                                        # Pull application changes from Git
  gdk reconfigure                                   # Delete and regenerate all config files created by GDK
  gdk diff-config                                   # Preview `gdk reconfigure` config changes
  gdk switch <branch>                               # Switch to a branch and set up services (similar to `gdk update`)

  gdk config list                                   # List all config keys and values
  gdk config get postgresql.port                    # Get configuration value
  gdk config set postgresql.port <port>             # Set configuration value
  gdk config set sshd.hostkeyalgorithms rsa,ed25519 # Set array configuration with comma-separated values

  gdk telemetry                                     # Opt in or out of error tracking and analytic data collection

  gdk reset-data                                    # Back up and create fresh Gitaly storage, PostgreSQL
                                                    #  data and Rails upload directory
  gdk reset-praefect-data                           # Back up and create fresh Praefect PostgreSQL data
  gdk reset-registry-data                           # Clean up existing Registry PostgreSQL data 
                                                    # and reinstate a fresh registry database.
  gdk import-registry-data                          # One-off import of the Registry data into the 
                                                    # Registry metadata database.
  gdk cleanup                                       # Truncate log files and remove any unnecessarily
                                                    #  installed dependencies
  gdk pristine                                      # Reset GDK state. Deletes caches, temporary files
                                                    #  reinstalls dependencies. Does not delete data

  gdk doctor                                        # Run diagnostics on GDK
  gdk console                                       # Run IRB with GDK context
  gdk version                                       # Print current GDK version
  gdk help                                          # Print this help text

Troubleshoot GDK:
  gdk report                                        # Generate a detailed support issue to get help with troubleshooting

Development tools:

  gdk predictive                                    # Run relevant tests for your local changes, whether committed or not
  gdk sandbox                                       # Create an ephemeral database sandbox

# Development admin account: root / 5iveL!fe

For more information about GitLab development see
https://docs.gitlab.com/ee/development/index.html.
