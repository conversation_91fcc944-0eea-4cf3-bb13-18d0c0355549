variables:
  R<PERSON><PERSON><PERSON><PERSON>Y_FILES_API_URL: "${CI_API_V4_URL}/projects/278964/repository/files"
  GITLAB_OPENBAO_VERSION_FILE_URL: "${REPOSITORY_FILES_API_URL}/GITLAB_OPENBAO_VERSION/raw?ref=master"
  GITLAB_SHELL_VERSION_FILE_URL: "${REPOSITORY_FILES_API_URL}/GITLAB_SHELL_VERSION/raw?ref=master"
  GITLAB_WORKHORSE_VERSION_FILE_URL: "${REPOSITORY_FILES_API_URL}/GITLAB_WORKHORSE_VERSION/raw?ref=master"
  GITALY_SERVER_VERSION_FILE_URL: "${REPOSITORY_FILES_API_URL}/GITALY_SERVER_VERSION/raw?ref=master"

.compile-job-base:
  stage: build
  extends:
    - .rules:compile-binary
  artifacts:
    when: on_success
    paths:
      - '**/checksums.txt'
      - '**/metadata.txt'
    expire_in: 14d

.compile-on-linux-base:
  extends:
    - .compile-job-base
  image: registry.gitlab.com/gitlab-org/gitlab-omnibus-builder/ubuntu_22.04:5.27.0
  tags:
    - gitlab-org
  variables:
    BUILD_OS: "linux"

.compile-on-linux:
  extends: !reference [.compile-on-linux-$BUILD_ARCH]
  extends:
    - .compile-on-linux-base
  parallel:
    matrix:
      - BUILD_ARCH:
        - "amd64"
        - "arm64"

.compile-on-linux-amd64:
  extends:
    - .compile-on-linux-base
  variables:
    BUILD_ARCH: "amd64"

.compile-on-linux-arm64:
  extends:
    - .compile-on-linux-base
  image: registry.gitlab.com/gitlab-org/gitlab-omnibus-builder/ubuntu_22.04_arm64:5.27.0
  tags:
    - saas-linux-medium-arm64
  variables:
    BUILD_ARCH: "arm64"

.compile-on-macos:
  extends:
    - .compile-job-base
  image: macos-14-xcode-15
  tags:
    - saas-macos-medium-m1
  variables:
    BUILD_OS: "darwin"
    BUILD_ARCH: "arm64"
    MISE_HTTP_TIMEOUT: "60s"
    MISE_FETCH_REMOTE_VERSIONS_TIMEOUT: "60s"

compile:linux:gitlab-shell:
  extends:
    - .compile-on-linux
  script:
    - support/ci/setup-compile gitlab-shell

compile:macos:gitlab-shell:
  extends:
    - .compile-on-macos
  script:
    - support/ci/setup-compile gitlab-shell

compile:linux:gitlab-workhorse:
  extends:
    - .compile-on-linux
  script:
    - support/ci/setup-compile workhorse

compile:macos:gitlab-workhorse:
  extends:
    - .compile-on-macos
  image: macos-14-xcode-15
  script:
    - support/ci/setup-compile workhorse

# Gitaly compiles `git` binaries, so it's easier to use an arm64
# runner than set up for cross-compiling.
compile:linux-x86:gitaly:
  extends:
    - .compile-on-linux-amd64
  script:
    - support/ci/setup-compile gitaly

compile:linux-arm64:gitaly:
  extends:
    - .compile-on-linux-arm64
  script:
    - support/ci/setup-compile gitaly

compile:macos:gitaly:
  extends:
    - .compile-on-macos
  script:
    - support/ci/setup-compile gitaly

compile:linux:openbao:
  extends:
    - .compile-on-linux
  script:
    - support/ci/setup-compile openbao

compile:macos:openbao:
  extends:
    - .compile-on-macos
  image: macos-14-xcode-15
  script:
    - support/ci/setup-compile openbao

packages-cleanup:
  extends:
    - .rules:packages-cleanup
  stage: cleanup
  image: ${GITLAB_DEPENDENCY_PROXY}ruby:${RUBY_VERSION}
  before_script:
    - gem install httparty --no-document --version 0.20.0
    - gem install gitlab --no-document --version 4.19.0
  script:
    - support/package-cleanup

export-database:
  extends:
    - .rules:compile-binary
  image: $VERIFY_IMAGE_MAIN
  stage: build
  needs: []
  tags:
    - saas-linux-large-amd64
  variables:
    GIT_STRATEGY: none
  script:
    - cd /home/<USER>/gdk
    - git fetch
    - git checkout $CI_COMMIT_REF_NAME
    - git show HEAD
    - gem install gitlab-sdk sentry-ruby zeitwerk tty-spinner
    - gdk start postgresql
    - sleep 5
    - pg_isready -t 60 --host=$(pwd)/postgresql --port=5432
    - pg_dumpall --host=$(pwd)/postgresql --port=5432 > postgres.sql
    - tar czf gdk_preseeded_db.tar.gz postgres.sql repositories gitlab/config/secrets.yml
    - support/package-helper gdk_preseeded_db upload
    - mv gdk_preseeded_db.tar.gz $CI_PROJECT_DIR
  artifacts:
    expire_in: 7 days
    when: always
    paths:
      - gdk_preseeded_db.tar.gz
