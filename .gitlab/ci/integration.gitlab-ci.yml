########################
# Base job definitions #
########################

.integration-base-job:
  extends:
    - .verify-job-cached_variables
    - .rules:code-changes-verify
  image: ${INTEGRATION_IMAGE}
  stage: integration
  needs:
    - build-integration-image
  tags:
    - gitlab-org-docker
  variables:
    GIT_STRATEGY: none
  before_script:
    - cd /home/<USER>/gdk
    - !reference [.default-before_script, before_script]
    - gdk config set tool_version_manager.enabled true
  after_script:
    - !reference [.default-after_script, after_script]
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
    expire_in: 2 days
    when: always
  timeout: 2h
  allow_failure: true

##################
# Shared scripts #
##################

.golang-upgrade-common-script: &golang-upgrade-common-script
  - |
    CURRENT_GO_VERSION=$(grep '^golang ' ${TOOL_VERSIONS_PATH} | awk '{print $2}')
    echo "Current Go version: $CURRENT_GO_VERSION"
    echo "Target Go version: $GO_VERSION"
    if [ "$CURRENT_GO_VERSION" = "$GO_VERSION" ]; then
      echo "Skipping job since the current Go version ($CURRENT_GO_VERSION) is the same as the target version ($GO_VERSION)."
      exit 0
    fi
  - source support/ci/functions.sh
  - init
  - cd_into_checkout_path

.golang-upgrade-script: &golang-upgrade-script
  - sed -i "s/golang .*/golang ${GO_VERSION}/" $TOOL_VERSIONS_PATH
  - cd ${UPDATE_PATH:-./}
  - run_timed_command "GDK_SELF_UPDATE=0 gdk update"
  - run_timed_command "go version"
  - run_timed_command "gdk start"
  - run_timed_command "test_url"

###############################
# Integration job definitions #
###############################

integration:start:
  extends: .integration-base-job
  script:
    - run_timed_command "support/ci/verify-start"

integration:vite:
  extends: .integration-base-job
  script:
    - source support/ci/functions.sh
    - init
    - cd_into_checkout_path
    - gdk config set webpack.enabled false
    - gdk config set vite.enabled true
    - run_timed_command "gdk reconfigure"
    - run_timed_command "gdk start"
    - run_timed_command "test_url"

integration:update-from-gitlab:
  extends: .integration-base-job
  script:
    - source support/ci/functions.sh
    - init
    - cd_into_checkout_path "gitlab"
    - run_timed_command "GDK_SELF_UPDATE=0 gdk update"
    - run_timed_command "gdk config list"
    - run_timed_command "gdk reconfigure"
    - run_timed_command "gdk start"
    - run_timed_command "test_url"

integration:golang-upgrade-from-gitlab:
  extends: .integration-base-job
  variables:
    TOOL_VERSIONS_PATH: gitlab/workhorse/.tool-versions
    UPDATE_PATH: gitlab/workhorse
  script:
    - *golang-upgrade-common-script
    - *golang-upgrade-script

integration:postgres-upgrade:
  extends:
    - .integration-base-job
    - .rules:postgres-upgrade
  script:
    - |
      CURRENT_POSTGRES_VERSION=$(grep '^postgres ' .tool-versions | awk '{print $2}')
      echo "Current version: $CURRENT_POSTGRES_VERSION"
      echo "Target version: $TARGET_POSTGRES_VERSION"
      if [ "$CURRENT_POSTGRES_VERSION" = "$TARGET_POSTGRES_VERSION" ]; then
        echo "Skipping job since the current is the same as the target version ($TARGET_POSTGRES_VERSION)."
        exit 0
      fi
    - source support/ci/functions.sh
    - init
    - cd_into_checkout_path "gitlab"
    - sed -i -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - cd_into_checkout_path
    - sed -i -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - run_timed_command "gdk config set pgvector.enabled true"
    - run_timed_command "GDK_SELF_UPDATE=0 gdk update"
    - run_timed_command "gdk start"
    - run_timed_command "test_url"

integration:cells:
  extends: .integration-base-job
  variables:
    TOOL_VERSIONS_PATH: gitlab/workhorse/.tool-versions
    UPDATE_PATH: gitlab/workhorse
  script:
    - gdk config set cells.enabled true
    - gdk config set cells.instance_count 1
    - gdk reconfigure
    - gdk cells up
  rules:
    - if: '$CI_MERGE_REQUEST_LABELS =~ /pipeline::expedited/'
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - .gitlab/ci/integration.gitlab-ci.yml
        - lib/gdk/command/cells.rb
        - lib/cell_manager.rb
        - lib/gdk/services/gitlab_topology_service.rb
        - lib/gdk/services/gitlab_http_router.rb
        - support/templates/gitlab-topology-service/config.toml.erb
    - !reference [.rules:code-changes-verify, rules]

integration:performance:
  extends:
    - .integration-base-job
    - .rules:code-changes-mr-only
  tags: [saas-linux-large-amd64]
  variables:
    HYPERFINE_VERSION: v1.19.0
    HYPERFINE_PACKAGE_NAME: hyperfine_1.19.0_amd64.deb
  before_script:
    - !reference [.integration-base-job, before_script]
    - wget https://github.com/sharkdp/hyperfine/releases/download/${HYPERFINE_VERSION}/${HYPERFINE_PACKAGE_NAME}
    - sudo dpkg -i ${HYPERFINE_PACKAGE_NAME}
    - rm ${HYPERFINE_PACKAGE_NAME}
  script:
    - support/ci/performance
