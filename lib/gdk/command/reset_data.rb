# frozen_string_literal: true

require 'net/http'

module GDK
  module Command
    # Handles `gdk reset-data` command execution
    class ResetData < BaseCommand
      def run(args = [])
        raise UserInteractionRequired, 'Cannot reset data while in sandbox mode. Run `gdk sandbox disable` to disable it.' if SandboxManager.new(config: config).enabled?

        fast = args.delete('--fast')
        abort_wrong_default_branch if fast && config.gitlab.default_branch != 'master'

        return false unless continue?
        return false unless stop_and_backup!

        return reset_data_fast! if fast

        reset_data!
      end

      private

      def abort_wrong_default_branch
        default_branch = out.wrap_in_color(config.gitlab.default_branch, out::COLOR_CODE_BLUE)
        setting = out.wrap_in_color('gitlab.default_branch', out::COLOR_CODE_YELLOW)
        master = out.wrap_in_color('master', out::COLOR_CODE_BLUE)
        fast = out.wrap_in_color('--fast', out::COLOR_CODE_YELLOW)

        raise UserInter<PERSON>Required, "Your default GitLab branch is #{default_branch}. Please set #{setting} to #{master} to use #{fast}."
      end

      def stop_and_backup!
        Runit.stop(quiet: true)

        return true if backup_data

        GDK::Output.error('Failed to backup data.')
        display_help_message

        false
      end

      def reset_data_fast!
        GDK::Command::Stop.new.run
        sleep 5
        result = GDK.make('ensure-databases-setup')
        unless result.success?
          GDK::Output.error('Failed to reset data.', result.stderr_str)
          display_help_message

          return false
        end

        rake = GDK::Execute::Rake.new(%w[db:create])
        unless rake.execute_in_gitlab(retry_attempts: 3).success?
          GDK::Output.error('Failed to create databases.', rake.stderr_str)
          display_help_message

          return false
        end

        helper = GDK::PackageHelper.new(package: :gdk_preseeded_db)
        package_path = helper.download_package(skip_cache: true, return_path: true)

        GDK::Shellout.new(%W[tar xf #{package_path}]).stream

        cmd = GDK::Postgresql.new.psql_cmd([])
        pid = Kernel.spawn(*cmd, in: 'postgres.sql')
        _, status = Process.wait2(pid)
        out.abort("Failed to restore database from postgres.sql (exit code: #{status.exitstatus})") unless status.success?

        out.notice('Successfully reset with preeseeded database!')
        out.info("Migrations may be pending. If GDK restart fails, try: #{out.wrap_in_color('gdk rails db:migrate', out::COLOR_CODE_YELLOW)}")
        true
      ensure
        FileUtils.rm_f(package_path) if defined?(package_path) && package_path.is_a?(String)
        FileUtils.rm_f(config.gdk_root.join('postgres.sql'))
      end

      def reset_data!
        result = GDK.make('gitlab-**********************', 'ensure-databases-setup', 'reconfigure')

        if result.success?
          GDK::Output.notice('Successfully reset data!')
          GDK::Command::Start.new.run
        else
          GDK::Output.error('Failed to reset data.', result.stderr_str)
          display_help_message

          false
        end
      end

      def continue?
        GDK::Output.warn("We're about to remove _all_ (GitLab and praefect) PostgreSQL data, Rails uploads and git repository data.")
        GDK::Output.warn("Backups will be made in '#{GDK.root.join('.backups')}', just in case!")

        return true if ENV.fetch('GDK_RESET_DATA_CONFIRM', 'false') == 'true' || !GDK::Output.interactive?

        GDK::Output.prompt('Are you sure? [y/N]').match?(/\Ay(?:es)*\z/i)
      end

      def backup_data
        move_postgres_data && move_redis_dump_rdb && move_rails_uploads && move_git_repository_data
      end

      def current_timestamp
        @current_timestamp ||= Time.now.strftime('%Y-%m-%d_%H.%M.%S')
      end

      def create_directory(directory)
        directory = gdk_root_pathed(directory)
        Dir.mkdir(directory) unless directory.exist?

        true
      rescue Errno::ENOENT => e
        GDK::Output.error("Failed to create directory '#{directory}' - #{e}", e)
        false
      end

      def backup_path(message, *path)
        path_to_backup = gdk_backup_pathed_timestamped(*path)
        path = gdk_root_pathed(*path)
        return true unless path.exist?

        GDK::Output.notice("Moving #{message} from '#{path}' to '#{path_to_backup}/'")

        # Ensure the base path exists
        FileUtils.mkdir_p(path_to_backup.dirname)
        FileUtils.mv(path, path_to_backup)

        true
      rescue SystemCallError => e
        GDK::Output.error("Failed to rename path '#{path}' to '#{path_to_backup}/' - #{e}", e)
        false
      end

      def gdk_backup_pathed_timestamped(*path)
        path = path.flatten
        path[-1] = "#{path[-1]}.#{current_timestamp}"
        GDK.root.join('.backups', *path)
      end

      def gdk_root_pathed(*path)
        GDK.root.join(*path.flatten)
      end

      def move_postgres_data
        backup_path('PostgreSQL data', %w[postgresql data])
      end

      def move_redis_dump_rdb
        backup_path('redis dump.rdb', %w[redis dump.rdb])
      end

      def move_rails_uploads
        backup_path('Rails uploads', %w[gitlab public uploads])
      end

      def move_git_repository_data
        backup_path('git repository data', 'repositories') &&
          restore_repository_data_dir &&
          backup_path('more git repository data', 'repository_storages')
      end

      def restore_repository_data_dir
        FileUtils.mkdir_p('repositories')
        true
      end

      def touch_file(file)
        FileUtils.touch(file)
        true
      rescue SystemCallError
        false
      end
    end
  end
end
