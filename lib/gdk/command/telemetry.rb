# frozen_string_literal: true

module GDK
  module Command
    class Telemetry < BaseCommand
      def run(_ = [])
        if GDK::Telemetry.team_member?
          out.info('GD<PERSON> has detected that you are a GitLab team member.')
          out.notice('Telemetry is always enabled for team members.')
          return true
        end

        print GDK::Telemetry::PROMPT_TEXT
        answer = $stdin.gets&.strip&.downcase

        case answer
        when nil
          puts 'No input received. Keeping previous behavior.'
        when 'y', 'n'
          GDK::Telemetry.update_settings(answer)
          puts tracking_message
        else
          puts 'Input not valid. Keeping previous behavior.'
        end

        true
      rescue Interrupt
        puts
        puts "Keeping previous behavior: #{tracking_message}"
        true
      end

      private

      def tracking_message
        if GDK::Telemetry.telemetry_enabled?
          'Telemetry is enabled. Data will be collected anonymously.'
        else
          'Telemetry is disabled. No data will be collected.'
        end
      end
    end
  end
end
