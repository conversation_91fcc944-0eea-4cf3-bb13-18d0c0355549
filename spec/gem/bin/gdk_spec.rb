# frozen_string_literal: true

RSpec.describe 'gdk' do
  let!(:gdk_bin_full_path) do
    File.expand_path(File.join(File.dirname(__FILE__), '..', '..', '..', 'gem', 'bin', 'gdk'))
  end

  shared_examples 'returns expected output' do
    it 'returns expected output' do
      expect(run("GDK_TELEMETRY=0 #{gdk_bin_full_path} #{command}")).to eql(expected_output)
    end
  end

  shared_examples 'contains expected output' do
    it 'contains expected output' do
      expect(run("GDK_TELEMETRY=0 #{gdk_bin_full_path} #{command}")).to include(expected_output)
    end
  end

  describe 'help' do
    let(:expected_output) { "Usage: gdk <command> [<args>]" }

    %w[help -help --help].each do |variant|
      context variant do
        let(:command) { variant }

        it_behaves_like 'contains expected output'
      end
    end
  end

  def run(command)
    Bundler.with_unbundled_env do
      `#{command}`
    end
  end
end
